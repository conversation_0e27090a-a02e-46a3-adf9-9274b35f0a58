# 装车审核页面批量审核功能实现说明

## 功能概述

在装车审核页面（`app/src/views/OrderManage/ApproveAssignVan/ViewApproveAssignVan.vue`）中实现了批量审核功能，允许用户勾选多张装车单进行批量审核操作。

## 实现的功能

### 1. 装车单勾选功能
- 在每个装车单项的右上角添加了复选框
- 用户可以单独勾选/取消勾选每张装车单
- 勾选状态会实时更新

### 2. 全选功能
- 在页面顶部添加了"全选"复选框
- 点击全选可以一次性选中/取消选中所有装车单
- 全选状态会根据单个勾选状态自动更新

### 3. 批量审核功能
- 当有装车单被选中时，底部会显示"批量审核"按钮
- 显示已选择的装车单数量
- 点击批量审核会弹出确认对话框
- 支持批量审核多张装车单

### 4. 审核进度显示
- 批量审核时显示加载状态和进度信息
- 显示当前正在审核的装车单序号
- 审核完成后显示成功/失败统计

## 技术实现细节

### 1. 数据结构修改
```javascript
// 添加的数据属性
data() {
  return {
    isChecked: false,        // 全选状态
    isApproving: false,      // 审核状态
    approvingMsg: '',        // 审核信息
    // ... 其他属性
  }
}

// 计算属性
computed: {
  selectedSheetsCount() {
    return this.deliveryList.filter(item => item.checked).length;
  }
}

// 数据初始化修复 - 确保checked属性在数据加载时就被初始化
res.data.forEach((item) => {
  item.checked = false; // 关键：初始化checked属性，解决Vue响应式更新问题
  this.deliveryList.push(item);
});
```

### 2. 界面组件
- 使用 `van-checkbox` 组件实现勾选功能
- 使用 `van-button` 组件实现批量审核按钮
- 使用 `van-loading` 组件显示审核进度
- 使用 `van-dialog` 组件显示确认对话框

### 3. 核心方法

#### 全选/取消全选
```javascript
selectAll(isChecked) {
  this.isChecked = isChecked;
  this.deliveryList.forEach((item) => {
    item.checked = isChecked;
  });
}
```

#### 单个复选框处理
```javascript
handleCheckBox(index) {
  const allChecked = this.deliveryList.every(item => item.checked);
  this.isChecked = allChecked;
}
```

#### 批量审核入口
```javascript
async batchApprove() {
  const selectedSheets = this.deliveryList.filter(item => item.checked);
  if (selectedSheets.length === 0) {
    Toast.fail('请选择要审核的单据');
    return;
  }

  Dialog.confirm({
    title: '批量审核',
    message: `确认要审核选中的 ${selectedSheets.length} 张装车单吗？`,
    width: "320px"
  }).then(() => {
    this.doBatchApprove(selectedSheets);
  });
}
```

#### 执行批量审核
```javascript
async doBatchApprove(sheets) {
  this.isApproving = true;
  this.approvingMsg = '正在批量审核...';

  let successCount = 0;
  let failCount = 0;
  const failedSheets = [];

  for (let i = 0; i < sheets.length; i++) {
    const sheet = sheets[i];
    this.approvingMsg = `正在审核第 ${i + 1}/${sheets.length} 张装车单...`;

    try {
      await this.approveSingleSheet(sheet);
      successCount++;
      sheet.checked = false;
    } catch (error) {
      failCount++;
      failedSheets.push({
        sheet_no: sheet.op_no || sheet.order_sheet_no,
        error: error.message
      });
    }
  }

  this.isApproving = false;
  this.isChecked = false;
  this.newQuery(); // 刷新列表

  // 显示结果
  if (failCount === 0) {
    Toast.success(`批量审核完成，共审核 ${successCount} 张装车单`);
  } else {
    Toast(`审核完成：成功 ${successCount} 张，失败 ${failCount} 张`);
  }
}
```

#### 双重审核策略
实现了两种审核方法，提高审核成功率：

1. **简化审核方法**（优先使用）：
```javascript
approveAssignVanDirectly(sheet) {
  return new Promise((resolve, reject) => {
    // 检查必要的装车单信息
    if (!sheet.op_id) {
      reject(new Error('装车单ID不存在'));
      return;
    }

    // 构造基于现有装车单的审核参数
    const approveParams = {
      op_id: sheet.op_id,
      submitType: 'approve',
      fromApp: true,
      moveStock: true
    };

    // 如果装车单已有完整信息，添加到参数中
    if (sheet.to_van) {
      approveParams.vanID = sheet.to_van;
    }
    if (sheet.senders_id && sheet.senders_name) {
      approveParams.senders = [sheet.senders_id + ',' + sheet.senders_name];
    }

    // 直接调用审核API
    AssignVan(approveParams).then((approveRes) => {
      if (approveRes.result === "OK") {
        resolve(approveRes);
      } else {
        reject(new Error(approveRes.msg || '审核失败'));
      }
    });
  });
}
```

2. **完整审核方法**（备用方案）：
```javascript
approveSingleSheet(sheet) {
  return new Promise((resolve, reject) => {
    // 1. 获取装车单详细信息
    GetAssignedOrders({ op_id: sheet.op_id }).then((res) => {
      if (res.result === "OK") {
        // 2. 准备送货员信息
        var senders = []
        if (sheet.senders_id && sheet.senders_name) {
          var sendersID = sheet.senders_id.split(',')
          var sendersName = sheet.senders_name.split(',')
          for (let i = 0; i < sendersID.length; i++) {
            if (sendersID[i] && sendersName[i]) {
              senders.push(sendersID[i] + ',' + sendersName[i])
            }
          }
        }

        // 3. 构造完整的审核参数 - 参考ShowAssignVanInfo的参数结构
        const approveParams = {
          sheetIDs: res.assignSheet.sale_order_sheets_id,
          moveStock: res.moveStock !== undefined ? res.moveStock : true,
          sumSheet: res.sumSheet,
          vanID: sheet.to_van,
          senders: senders,
          sheets: res.oldSheets || [],
          newSheets: res.sheets || res.oldSheets || [],
          fromApp: true,
          submitType: 'approve',
          op_id: sheet.op_id,
          op_no: sheet.op_no
        };

        // 4. 调用审核API
        AssignVan(approveParams).then((approveRes) => {
          if (approveRes.result === "OK") {
            resolve(approveRes);
          } else {
            reject(new Error(approveRes.msg || '审核失败'));
          }
        });
      } else {
        reject(new Error(res.msg || '获取装车单信息失败'));
      }
    });
  });
}
```

3. **智能审核策略**：
```javascript
// 在批量审核中优先使用简化方法，失败时自动切换到完整方法
try {
  await this.approveAssignVanDirectly(sheet);
} catch (directError) {
  // 如果简化方法失败，尝试使用完整的审核方法
  await this.approveSingleSheet(sheet);
}
```

### 4. 数据初始化修复
确保在数据加载时就初始化 `checked` 属性：
```javascript
res.data.forEach((item) => {
  item.checked = false; // 关键：初始化checked属性
  this.deliveryList.push(item);
});
```

### 5. 样式设计
```css
/* 批量审核按钮样式 */
.batch-approve-btn {
  background-color: #ff9999 !important;
  color: #fff !important;
  border: none !important;
  border-radius: 15px !important;
  height: 30px !important;
  padding: 0 16px !important;
  font-size: 14px !important;
  white-space: nowrap !important;
  margin-left: 8px !important;
}

/* 批量审核加载状态样式 */
.approving-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.approving-modal {
  background-color: rgba(0, 0, 0, 0.8);
  padding: 20px;
  border-radius: 8px;
  text-align: center;
  color: white;
}
```

## 使用流程

1. **选择装车单**：用户在装车审核列表页面勾选需要审核的装车单
2. **批量操作**：点击底部的"批量审核"按钮
3. **确认审核**：在弹出的确认对话框中确认操作
4. **执行审核**：系统逐个审核选中的装车单，显示进度
5. **查看结果**：审核完成后显示成功和失败的统计信息
6. **自动刷新**：列表自动刷新显示最新状态

## 错误处理

- 网络错误处理：捕获API调用异常
- 业务逻辑错误：显示后端返回的错误信息
- 用户操作错误：提示用户选择装车单
- 批量操作结果：统计成功和失败的数量，显示详细信息

## 性能优化

- 使用异步处理避免界面阻塞
- 逐个处理装车单避免并发过多
- 及时更新进度信息提升用户体验
- 操作完成后自动刷新列表数据

这个批量审核功能参考了批量打印的实现模式，结合了装车审核的业务逻辑，提供了完整的批量操作体验。

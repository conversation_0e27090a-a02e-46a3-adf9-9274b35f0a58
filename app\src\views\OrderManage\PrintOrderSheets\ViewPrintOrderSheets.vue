<template>
  <div class="public_query">
    <div class="public_query_title">
      <van-form >
        <div class="public_query_titleSrc">
          <div class="public_query_titleSrc_item">
            <input
              type="text"
              v-model="queryCondiValues.searchStr"
              placeholder="客户名/手机"
              @input="onClientNameInput"
            />
            <van-icon name="user-o" />
          </div>
          <div class="public_query_titleSrc_item">
            <YjSelectTree
            ref="selectTreeRef"
            :target="target"
            :title="title"
            :confirmColor="confirmColor"
            :rangeKey="rangeKey"
            :rootNode="rootNode"
		        :idKey="idKey"
            :sonNode="sonNode"
            :multipleCheck="multipleCheck"
            :parentSelectable="parentSelectable"
            :popupHeight="'90%'"
            @getRootNode="getRootNode"
            @handleConfirm="onRegionSelected"
            >
            <template #select-tree-content>
              <input
                type="text"
                v-model="queryCondiLabels.regionName"
                placeholder="选择片区"
                readonly
              />
              <van-icon name="wap-home-o" />
            </template>
          </YjSelectTree>
          </div>
        </div>
      </van-form>
      <!-- 日期选择组件 -->
      <yj-select-calendar-cache
        :start-time-fld-name.sync="queryCondiValues.startDate"
        :end-time-fld-name.sync="queryCondiValues.endDate"
        :cache-key="'PrintOrderSheetsCacheKey'"
        :default-days="6"
        @handleConfirm="onDateConfirm"
        :options-btn="[
          {key: '1-day', name: '今天'},
          {key: 'yesterday', name: '昨天'},
          {key: '7-day', name: '近7天'},
          {key: 'currentMonth', name: '本月'}
        ]"
      />


      <!-- 全选功能 -->
      <van-cell-group class="cellgroup" style="margin-top:2px;">
        <div style="display: flex; align-items: center; padding: 10px;">
          <van-checkbox v-model="isChecked" @change="selectAll" style="margin-right: 10px;">
            全选
          </van-checkbox>
        </div>
      </van-cell-group>
    </div>
    <van-pull-refresh
      :disabled="disabledPullRefresh"
      style="height: calc(100% - 168px);"
      v-model="pullRefreshLoading"
      @refresh="onRefresh"
    >
      <div class="sales_list_boxs" ref="sales_list_boxs" v-if="deliveryList.length > 0">
        <van-list
          v-model="loading"
          :finished="finished"
          finished-text="到底了~"
          @load="onNextPage"
        >
          <ul class="sales_ul" ref="sales_ul">
            <!--隐藏顶部 下拉距离参照-->
            <div ref="deliveryScroll"></div>
            <li v-for="(item, index) in deliveryList" @click.stop="onClientRowClick(item,$event)" :key="index" :ref="'sales_li' + item.order_sheet_no">
              <van-swipe-cell style="width:100%">
                <div class="sheet_wrapper" :style="{background:item.state=='submited'?'#eee':'#fff'}">
                  <div class="sup_info">
                    <div class="sup_name" >
                      {{ item.sup_name }}
                      <div v-if="item.order_source" class="order-source-wrapper">
                        {{item.order_source === 'xcx' ? '商城' : ''}}
                      </div>
                    </div>
                    <div class="sup_info_right">
                      <div v-if="item.sheet_print_count&&item.sheet_print_count!='0'" >印:{{item.sheet_print_count}}次
                      </div>
                      <div v-if="item.order_status" class="sheet_status">
                          {{item.order_status}}
                      </div>
                      <van-checkbox style="margin-left: 10px;" v-model="item.checked" @click="handleCheckBox(index)" id="check" />
                    </div>
                  </div>
                  <div class="sheet_info">
                    <div class="sheet_info_left">
                      <div class="sheet_no_tag" style="color: #1989fa; text-decoration: underline"  >
                        <div class="sheet_no" @click.stop="onSheetNoClick(item)">{{ item.order_sheet_no }}</div>
                      </div>
                      <div class="sheet_no_tag">
                        <div class="sheet_no" >{{ item.sale_sheet_no }}</div>
                      </div>
                      <div class="sheet_happentime" >
                        {{ item.happen_time }}
                      </div>
                    </div>
                    <div class="sheet_info_right">￥{{item.total_amount}}
                    </div>
                  </div>
                  <div class="sheet_add">
                    {{item.sup_addr}}
                  </div>
                  
                  <div class="seller_senders_info" >
                    <div v-if="item.seller_name">业务员: {{item.seller_name}}</div>
                    <div v-if="item.senders_name">送货员: {{item.senders_name}}</div>
                  </div>
                  <div class="mark-brief" v-if="item.make_brief">
                    备注: {{item.make_brief}}
                  </div>

                </div>
              </van-swipe-cell>
            </li>
          </ul>
        </van-list>
      </div>
    </van-pull-refresh>
    <div class="sales_list_boxs_no" v-if="deliveryList.length === 0">
      <div class="whole_box_no_icon iconfont">&#xe664;</div>
      <p>没有相关订单</p>
    </div>
    <!-- <van-popup
      style="overflow: hidden !important"
      v-model="showAddress"
      duration="0.4"
      position="bottom"
      :style="{ height: '90%', width: '100%' }"
    >
      <RegionSelection @onRegionSelected="onRegionSelected"></RegionSelection>
    </van-popup> -->
    <van-popup
      v-model="showChooseNavigationApp"
      position="bottom"
      :style="{ height: '30%' }"
    >
      <div
        class="navi-select-item"
        @click="onNaviSelectItem(item)"
        v-for="(item, index) in navigationAppList"
        :key="index"
      >
        {{ item.name }}
      </div>
    </van-popup>

    <div class="wrapper">
      <div class="content">
        共<span class="record">{{ total }}</span>条
      </div>
      <div v-if="selectedSheetsCount > 0" class="content">
        已选<span class="record">{{ selectedSheetsCount }}</span>单
      </div>
      <van-button @click="batchPrint" class="batch-print-btn" v-if="selectedSheetsCount > 0">
        批量打印
      </van-button>
    </div>

    <!-- 打印加载状态 -->
    <div v-if="isPrinting" class="printing-overlay">
      <div class="printing-modal">
        <van-loading type="spinner" color="#ffffff" size="24px" vertical>
          {{ loadingMsg }}
        </van-loading>
      </div>
    </div>

    <!-- 模板选择弹窗 -->
    <van-popup v-model="showTemplateSelection" position="bottom" round>
      <div class="template-selection-wrapper">
        <div class="template-header">
          <h3>选择打印模板</h3>
          <van-icon name="cross" @click="showTemplateSelection = false" class="close-icon" />
        </div>
        <div class="template-content">
          <div v-if="templateList.length === 0" class="no-template">
            没有可用的打印模板
          </div>
          <div v-else class="template-list">
            <div v-for="(template, index) in templateList" :key="index" class="template-item">
              <van-radio v-model="selectedTemplateIndex" :name="index">
                {{ template.name }}
              </van-radio>
            </div>
          </div>
        </div>
        <div class="template-footer">
          <van-button @click="showTemplateSelection = false" class="cancel-btn">取消</van-button>
          <van-button type="primary" @click="confirmTemplateSelection" class="confirm-btn">确认</van-button>
        </div>
      </div>
    </van-popup>
  </div>
</template>
<script>
import {SwipeCell,Cell,CellGroup,Toast,PullRefresh,Tag,Form,Icon,List,Popup,Field,Dialog,Checkbox,Button,Loading,Radio} from "vant";
import {
  GetOrdersForPrint,
  SaleOrderSheetReject,
  SaleOrderSheetRecover,
  TransformToGaodePositionRequest,
  AppGetSheetToPrint,
  AppGetSheetToPrint_Post,
  AppSheetToEsc,
  AppCloudPrint_sheetTmp,
  AppCloudPrint_escCmd,
  AppSheetToImages,
  AppGetTemplate,
  ApiPrintMark
} from "../../../api/api";
import Printing from "../../Printing/Printing";
import  Navi from '../../service/Navi'
// import RegionSelection from "../../components/RegionSelection";
import YjSelectTree from "../../components/yjTree/YjSelectTree.vue";
import YjSelectCalendarCache from "../../components/yjSelect/yjCalendar/YjSelectCalendarCache.vue";
import Position from '../../../components/Position';
export default {
  name: "ViewPrintOrderSheets",
  data() {
    return {
      pullRefreshLoading: false,
      disabledPullRefresh: false,
      isChecked: false,
      selectedSheets: [],
      isPrinting: false,
      loadingMsg: '',
      showTemplateSelection: false,
      templateList: [],
      selectedTemplateIndex: 0,
      templatesLoaded: false,
      printTemplates: [],
      printTemplatesTiny: [],
      initialQueryDone: false,
      coords: {},
      list: [],
      loading: false,
      finished: false,
      pageSize: 20,
      startRow: 0,
      getTotal: true,
      navigationAppList: [
        {
          id: 1,
          name: "高德地图",
        },
        {
          id: 2,
          name: "百度地图",
        },
      ],
      showChooseNavigationApp: false,
      deliveryList: [],
      showAddress: false,
      selectedSupcustNavigatorInfo: {},
      total: 0,

      target:'region',
      rangeKey: 'name',
			idKey: 'id',
      sonNode:'subNodes',
      asPage:true,
			multipleCheck: true,
			parentSelectable: true,
			foldAll: true,
			confirmColor:'#e3a2a2',
			title: '片区选择',
      rootNode:{}
    };
  },
  props: {
    queryCondiValues:Object,
    queryCondiLabels:Object,
  },
  activated() {
    //this.onRefresh()
  },
  async mounted() {
    window.addEventListener("scroll", this.handleScrollY, true);
    var operRegions = this.$store.state.operInfo.operRegions;
    if (operRegions) {
      operRegions = JSON.stringify(operRegions);
      //operRegions=operRegions.replace('[').replace(']')
      this.queryCondiValues.operRegions = operRegions;
    }
    this.deliveryList.length = 0;
    // this.newQuery()
  },
  onHide() {
    window.removeEventListener("scroll", this.handleScrollY, false);
  },
  components: {
    "van-form": Form,
    "van-icon": Icon,
    "van-list": List,
    "van-popup": Popup,
    "van-tag": Tag,
    "van-pull-refresh": PullRefresh,
    "van-swipe-cell": SwipeCell,
    "van-cell": Cell,
    "van-cell-group": CellGroup,
    "van-field": Field,
    "van-checkbox": Checkbox,
    "van-button": Button,
    "van-loading": Loading,
    "van-radio": Radio,
    [Dialog.Component.name]: Dialog.Component,
    // RegionSelection,
    YjSelectTree,
    "yj-select-calendar-cache": YjSelectCalendarCache
  },
  computed: {
    selectedSheetsCount() {
      return this.deliveryList.filter(item => item.checked).length;
    }
  },
  methods: {
    handleScrollY() {
      //思路：监听参照元素和下拉位置的距离，当距离接近参照元素时可以进行下拉，当远离参照元素时，禁止下拉。
      //因为是下拉，所以与距离为负数 当与参照距离过远则禁用下拉组件 反之上拉到一定位置时，则开启下拉组件 (zxk)
      //console.log(this.$refs.deliveryScroll == undefined);
      if (this.$refs.deliveryScroll == undefined) {
        return;
      }
      if (this.$refs.deliveryScroll.getBoundingClientRect().top <= -50) {
        this.disabledPullRefresh = true;
      } else {
        this.disabledPullRefresh = false;
      }
    },
    onRefresh() {

        this.newQuery();

        this.pullRefreshLoading = false;

    },

    newQuery() {
      this.startRow = 0;
      this.finished = false;
      this.deliveryList = [];
      this.startRow = 0;
      this.onNextPage();
    },

    // 触发初始查询（供父组件调用）
    triggerInitialQuery() {
      // 只有在日期已经初始化且还没有进行过初始查询时才执行
      if (this.queryCondiValues.startDate && this.queryCondiValues.endDate && !this.initialQueryDone) {
        this.newQuery();
        this.initialQueryDone = true;
      }
    },
    openNavigationAppDialog(item) {
      //打开弹出框并且把选中商家的导航信息存入全局变量
      this.selectedSupcustNavigatorInfo = {
        sup_addr: item.sup_addr,
        addr_lng: item.addr_lng,
        addr_lat: item.addr_lat,
      };

      this.showChooseNavigationApp = true;
    },
    onNaviSelectItem(item) {
      if (isiOS) {
        this.jumpiOSNaviUrlBySelectAppName(item.name);
      } else if(isHarmony){
        this.jumpHarmonyNaviUrlBySelectAppName(item.name);
      } else{
        this.jumpAndroidNaviUrlBySelectAppName(item.name);
      }
      //隐藏弹出框
      this.showChooseNavigationApp = false;
    },
    async getCurPosition(){
        const positionService=new Position(isiOS)
        const position = await positionService.currentPosition()
        console.log(position)
        return position
    },
    async jumpHarmonyNaviUrlBySelectAppName(name) {
      //从全局变量中取选中商家的导航信息
      const navigationInfo = this.selectedSupcustNavigatorInfo;
      const { addr_lng, addr_lat, sup_addr } = navigationInfo
      const { longitude, latitude }=await this.getCurPosition()
      if (name == "百度地图") {
        const navi = new Navi("baidu", isiOS, addr_lng + "," + addr_lat, sup_addr, isHarmony)
        var ref = cordova.InAppBrowser.open(navi.getBaiduUrl(), "_system");
        ref.show();
      }
      if (name == "高德地图") {
        const navi = new Navi("gaode", isiOS,addr_lng + "," + addr_lat, sup_addr, isHarmony, longitude + "," + latitude)
        var ref = cordova.InAppBrowser.open(await navi.getGaoDeUrl(), "_system");
        ref.show();
      }
    },
 async jumpiOSNaviUrlBySelectAppName(name) {
      //从全局变量中取选中商家的导航信息
      const navigationInfo = this.selectedSupcustNavigatorInfo;
      const{addr_lng,addr_lat,sup_addr}=navigationInfo
      if (name == "百度地图") {
        const navi=new Navi("baidu",isiOS,addr_lng+","+addr_lat,sup_addr)
        cordova.InAppBrowser.open(navi.getBaiduUrl(),"_system");
      }
      if (name == "高德地图") {
          const navi=new Navi("gaode",isiOS,addr_lng+","+addr_lat,sup_addr)
          cordova.InAppBrowser.open(await navi.getGaoDeUrl(),"_system");

      }
    },

   async jumpAndroidNaviUrlBySelectAppName(name) {
      //从全局变量中取选中商家的导航信息
      const navigationInfo = this.selectedSupcustNavigatorInfo;
      const{addr_lng,addr_lat,sup_addr}=navigationInfo
      if (name == "百度地图") {
        const navi=new Navi("baidu",isiOS,addr_lng+","+addr_lat,sup_addr)
        window.location.href = navi.getBaiduUrl()
      }
      if (name == "高德地图") {
          // const position = await this.baiduPoi2gaodePoi(addr_lng,addr_lat);
          // const lng = position.split(",")[0];
          // const lat = position.split(",")[1];
          const navi=new Navi("gaode",isiOS,addr_lng+","+addr_lat,sup_addr)
          window.location.href =await navi.getGaoDeUrl()
      }
    },
    onNextPage() {
      if (this.finished) return;
      if (this.finished) return;
      if(!window.isBoss()){
        if(this.$store.state.operInfo.operRights && this.$store.state.operInfo.operRights.delicacy && this.$store.state.operInfo.operRights.delicacy.sheetViewRange){
          if(this.$store.state.operInfo.operRights.delicacy.sheetViewRange.value=='self'){
            this.queryCondiValues.operID=this.$store.state.operInfo.oper_id
          }else if(this.$store.state.operInfo.operRights.delicacy.sheetViewRange.value =="department"){
            this.queryCondiValues.departID=this.$store.state.operInfo.depart_id;
          }else {
            this.queryCondiValues.departID=''
          }
        }
      }
      let params = {
        ...this.queryCondiValues,
        pageSize: this.pageSize,
        startRow: this.startRow,
        getTotal: this.getTotal,
        isBoss: window.isBoss()
      }
 
      GetOrdersForPrint(params).then((res) => {
        if (res.result === "OK") {
          if (this.finished) return
         // window.visitedSupcustIdList = res.visitedSupcustIdList;
          res.data.forEach((item) => {
            //item.isVisited = window.visitedSupcustIdList.indexOf(item.supcust_id) !== -1;
            if(item.last_visit_time) {
              var today=new Date(this.getDatePart(new Date()))
              if(new Date(item.last_visit_time)>today){
                 item.isVisited=true
              }
            }
            if(item.distance) {
              item.distanceStr = this.processDistanceAndFormatUnit(parseFloat(item.distance));
            }
            item.state=''
            var orderStatus ={
              zd:'已转单',
              xd:'已下单',
              dd:'已打单',
              hk:'已回库',
              zc:'已装车'
            }
            item.order_status = orderStatus[item.order_status]
            item.checked = false; // 初始化勾选状态
            this.deliveryList.push(item);
          });
          
          this.total = res.total;
          this.sheetList = res.data
          this.loading = false;
          this.startRow = Number(this.startRow) + this.pageSize;
          if (this.deliveryList.length >= Number(res.total)) {
            this.finished = true;
          }
        }
      });

      let that = this


    },
    isDistanceMoreOneKM(distance) {
      return distance > 1000;
    },
    processDistanceAndFormatUnit(distance) {
      let distanceStr = "";
      if (this.isDistanceMoreOneKM(distance)) {
        distance = distance / 1000;
        distanceStr = distance.toFixed(2) + " km ";
      } else {
        distanceStr = parseInt(distance) + " m ";
      }
      return distanceStr;
    },
    onClientRowClick(item, e) {
      // 如果点击的是复选框或其父元素，不执行跳转
      if (e.target.closest('#check') || e.target.closest('.van-checkbox')) {
        return;
      }
      if (e.target.className == 'sheet_checked') {
        return;
      }
      // 其他区域点击跳转到单据详情
      this.onSheetNoClick(item);
    },
    onSheetNoClick(item) {
      window.g_curSheetInList = item;
      window.g_curSheetList=this.sheetList
      this.$router.push({path:'/SaleSheet',query:{sheetID:item.order_sheet_id, sheetType:item.order_sheet_type}})
    },
    // 全选/取消全选
    selectAll(isChecked) {
      this.isChecked = isChecked;
      this.deliveryList.forEach((item) => {
        item.checked = isChecked;
      });
    },
    // 处理单个复选框变化
    handleCheckBox(index) {
      // 检查是否所有项都被选中
      const allChecked = this.deliveryList.every(item => item.checked);
      this.isChecked = allChecked;
    },
    getRootNode(node) {
      this.rootNode=node
    },
    onRegionSelected(region) {
      // if (region.regionID) {
      //   this.queryCondiLabels.regionName = region.regionName;
      //   this.queryCondiValues.regionID = region.regionID;
      // } else {
      //   this.queryCondiLabels.regionName = "";
      //   this.queryCondiValues.regionID = "";
      // }
      if(region.length>0){
        let regionID=[];
        let regionName=[];
        region.forEach((item)=>{
          regionID.push(item.id)
          regionName.push(item.name)
        })
        this.queryCondiLabels.regionName = regionName.join(',');
        this.queryCondiValues.regionID = regionID.join(',');
      }else {
        this.queryCondiLabels.regionName = "";
        this.queryCondiValues.regionID = "";
      }
      this.$refs.selectTreeRef.handleCancel()
      this.newQuery();
    },
    onClientNameInput() {
      this.newQuery();
    },
    // 日期选择确认回调
    onDateConfirm(startDate, endDate) {
      // YjSelectCalendarCache组件会自动更新queryCondiValues中的日期
      // 这里只需要触发查询
      this.newQuery();

      // 标记初始查询已完成
      if (!this.initialQueryDone) {
        this.initialQueryDone = true;
      }
    },
    // 批量打印
    async batchPrint() {
      const selectedSheets = this.deliveryList.filter(item => item.checked);
      if (selectedSheets.length === 0) {
        Toast.fail('请选择要打印的单据');
        return;
      }

      // 获取当前打印机信息
      const defaultPrinter = window.getDefaultPrinter();
      if (!defaultPrinter) {
        Toast.fail('请先在设置中配置默认打印机');
        return;
      }

      // 检查是否使用模板
      if (defaultPrinter.useTemplate) {
        // 需要选择模板
        await this.loadTemplatesForBatchPrint(selectedSheets);
      } else {
        // 不使用模板，直接打印
        this.showBatchPrintConfirm(selectedSheets, null);
      }
    },

    // 加载模板用于批量打印
    async loadTemplatesForBatchPrint(selectedSheets) {
      try {
        // 获取第一张单据的模板（假设同类型单据使用相同模板）
        const firstSheet = selectedSheets[0];
        const templates = await this.loadPrintTemplatesForBatch(firstSheet.order_sheet_type, firstSheet.supcust_id);

        if (!templates || templates.length === 0) {
          Toast.fail('没有可用的打印模板');
          return;
        }

        // 根据打印机类型筛选模板
        const defaultPrinter = window.getDefaultPrinter();
        let printer_kind = defaultPrinter.kind;
        if (!printer_kind) {
          printer_kind = defaultPrinter.type === "cloud" ? '24pin' : 'tiny';
        }

        this.templateList = printer_kind === 'tiny' ?
          templates.filter(t => this.isSmallTemplate(t.tmp)) :
          templates;

        if (this.templateList.length === 0) {
          const errorMsg = printer_kind === 'tiny' ? '没有可用的小票模板' : '没有可用的打印模板';
          Toast.fail(errorMsg);
          return;
        }

        this.selectedTemplateIndex = 0;
        this.selectedSheets = selectedSheets;
        this.showTemplateSelection = true;

      } catch (error) {
        Toast.fail('加载打印模板失败: ' + error.message);
      }
    },

    // 判断是否为小票模板
    isSmallTemplate(template) {
      try {
        const templateContent = JSON.parse(template.template_content);
        return templateContent.width <= 110;
      } catch (e) {
        console.error('解析模板内容时出错:', template, e);
        return false;
      }
    },

    // 确认模板选择
    confirmTemplateSelection() {
      if (this.templateList.length === 0) {
        Toast.fail('没有可用的模板');
        return;
      }

      const selectedTemplate = this.templateList[this.selectedTemplateIndex];
      this.showTemplateSelection = false;
      this.showBatchPrintConfirm(this.selectedSheets, selectedTemplate);
    },

    // 显示批量打印确认对话框
    showBatchPrintConfirm(selectedSheets, template) {
      const defaultPrinter = window.getDefaultPrinter();
      const printerInfo = `当前打印机：${defaultPrinter.name || '默认打印机'}`;
      const templateInfo = template ? `\n打印模板：${template.name}` : '';

      Dialog.confirm({
        title: '批量打印',
        message: `${printerInfo}${templateInfo}\n\n确认要打印选中的 ${selectedSheets.length} 张单据吗？`,
        width: "320px"
      }).then(() => {
        this.doBatchPrint(selectedSheets, template);
      }).catch(() => {
        // 用户取消
      });
    },
    // 执行批量打印
    async doBatchPrint(sheets, template = null) {
      this.isPrinting = true;
      this.loadingMsg = '正在批量打印...';

      let successCount = 0;
      let failCount = 0;

      for (let i = 0; i < sheets.length; i++) {
        const sheet = sheets[i];
        this.loadingMsg = `正在打印第 ${i + 1}/${sheets.length} 张单据...`;

        try {
          await this.printSingleSheet(sheet, template);
          successCount++;
          // 打印成功后取消勾选
          sheet.checked = false;
        } catch (error) {
          console.error(`打印单据 ${sheet.order_sheet_no} 失败:`, error);
          failCount++;
        }

        // 添加延迟避免打印过快
        if (i < sheets.length - 1) {
          await new Promise(resolve => setTimeout(resolve, 1000));
        }
      }

      this.isPrinting = false;
      this.loadingMsg = '';

      // 更新全选状态
      this.isChecked = false;

      // 显示结果
      if (failCount === 0) {
        Toast.success(`批量打印完成，共打印 ${successCount} 张单据`);
      } else {
        Toast(`打印完成：成功 ${successCount} 张，失败 ${failCount} 张`);
      }
    },
    // 打印单张单据
    printSingleSheet(item, template = null) {
      return new Promise((resolve, reject) => {
        // 获取默认打印机
        const defaultPrinter = window.getDefaultPrinter();

        // 构建打印模板变量（只传入需要的变量，减少带宽占用）
        let printTemplate = [];
        var origTemplate=null
        if (template && template.tmp && template.tmp.template_content) {
          const sTmp = template.tmp.template_content;
          origTemplate=sTmp
          if (sTmp.indexOf('"prepay_balance"') !== -1) printTemplate.push({ name: "prepay_balance" });
          if (sTmp.indexOf('"arrears_balance"') !== -1) printTemplate.push({ name: "arrears_balance" });
          if (sTmp.indexOf('"print_count"') !== -1) printTemplate.push({ name: "print_count" });
          if (sTmp.indexOf('"name":"give_qty_unit"') >= 0) printTemplate.push({ name: "give_qty_unit" });
        }

        // 构建打印参数
        const params = {
          sheetType: item.order_sheet_type,
          sheet_type: item.order_sheet_type,
          sheet_id: item.order_sheet_id,
          smallUnitBarcode: false,
          printTemplate: JSON.stringify(printTemplate),
          copies: "1"
        };

        // 获取单据数据并打印 - 使用POST方法避免参数过长
        AppGetSheetToPrint_Post(params).then(data => {
          if (data.result !== 'OK') {
            reject(new Error(data.msg));
            return;
          }

          const sheet = data.sheet;

          // 根据打印机类型选择打印方式
          if (defaultPrinter.type === "cloud") {
            this.printWithCloudPrinter(sheet, defaultPrinter,origTemplate, resolve, reject);
          } else {
            this.printWithBluetoothPrinter(sheet, defaultPrinter,origTemplate, params, resolve, reject);
          }
        }).catch(err => {
          reject(err);
        });
      });
    },

    // 云打印机打印
    printWithCloudPrinter(sheet, defaultPrinter, template, resolve, reject) {
      // 判断是否使用模板
      if (defaultPrinter.useTemplate && template) {
        // 使用传入的模板打印
        const params = {
          operKey: this.$store.state.operKey,
          tmp:JSON.parse(template),
          sheet: sheet,
          printer_brand: defaultPrinter.brand,
          device_id: defaultPrinter.cloudID,
          check_code: defaultPrinter.cloudCode,
          cus_orderid: sheet.sheet_no,
          copies: "1"
        };

        AppCloudPrint_sheetTmp(params).then(res => {
          this.handleCloudPrintResult(res, sheet, resolve, reject);
        }).catch(err => {
          reject(new Error('云打印失败: ' + err.message));
        });
      } else {
        // 不使用模板，直接打印
        this.printCloudWithoutTemplate(sheet, defaultPrinter, resolve, reject);
      }
    },

    // 云打印不使用模板
    async printCloudWithoutTemplate(sheet, defaultPrinter, resolve, reject) {
      try {
        const b64Esc = await Printing._sheetToB64Esc(sheet, false, false);
        const params = {
          operKey: this.$store.state.operKey,
          tmp: { width: '80', height: '100' },
          sheet: sheet,
          printer_brand: defaultPrinter.brand,
          device_id: defaultPrinter.cloudID,
          check_code: defaultPrinter.cloudCode,
          cus_orderid: sheet.sheet_no,
          copies: "1",
          escCommand: b64Esc,
        };

        AppCloudPrint_escCmd(params).then(res => {
          this.handleCloudPrintResult(res, sheet, resolve, reject);
        }).catch(err => {
          reject(new Error('云打印失败: ' + err.message));
        });
      } catch (err) {
        reject(new Error('生成打印指令失败: ' + err.message));
      }
    },

    // 处理云打印结果
    handleCloudPrintResult(res, sheet, resolve, reject) {
      if (res.result === "OK" || res.return_msg === "打印成功" || (!res.printer_state && !res.return_msg)) {
        // 云打印本身已经有打印次数统计，不需要额外标记
        resolve();
      } else {
        const errorMsg = res.msg || res.printer_state || res.return_msg || '云打印失败';
        reject(new Error(errorMsg));
      }
    },

    // 蓝牙打印机打印
    printWithBluetoothPrinter(sheet, defaultPrinter, template, params, resolve, reject) {
      const printErrorHandler = (error) => {
        reject(new Error(error));
      };

      // 获取打印机类型
      let printer_kind = defaultPrinter.kind;
      if (!printer_kind) {
        printer_kind = 'tiny';
      }

      // 判断是否使用模板
      if (defaultPrinter.useTemplate && template) {
        // 使用传入的模板进行打印
        this.printBluetoothWithTemplate(sheet, template.tmp, printer_kind, params, resolve, reject);
      } else {
        // 不使用模板，直接打印
        this.printBluetoothWithoutTemplate(sheet, printer_kind, params, resolve, reject);
      }
    },

    // 蓝牙打印机使用模板打印
    printBluetoothWithTemplate(sheet, template, printer_kind, params, resolve, reject) {
      // 针式打印机或小票打印机的模板打印逻辑
      if (printer_kind === '24pin') {
        // 针式打印机
        const blocks = [];
        AppSheetToEsc(params).then(res => {
          const imageBytes = this.base64ToUint8Array(res);
          blocks.push({ imageBytes: imageBytes });

          Printing.printSheetOrInfo(blocks, (e) => {
            this.handleBluetoothPrintResult(e, sheet, resolve, reject);
          });
        }).catch(err => {
          reject(new Error('生成打印指令失败: ' + err.message));
        });
      } else {
        // 小票打印机
        AppSheetToImages({ ...params, tmp: template }).then(res => {
          const blocks = [];
          res.forEach(imgBase64 => {
            const imageBytes = this.base64ToUint8Array(imgBase64);
            blocks.push({ imageBytes: imageBytes });
          });

          Printing.printSheetOrInfo(blocks, (e) => {
            this.handleBluetoothPrintResult(e, sheet, resolve, reject);
          });
        }).catch(err => {
          reject(new Error('生成打印图片失败: ' + err.message));
        });
      }
    },

    // 蓝牙打印机不使用模板打印
    printBluetoothWithoutTemplate(sheet, printer_kind, params, resolve, reject) {
      if (printer_kind === '24pin') {
        // 针式打印机
        const blocks = [];
        AppSheetToEsc(params).then(res => {
          const imageBytes = this.base64ToUint8Array(res);
          blocks.push({ imageBytes: imageBytes });

          Printing.printSheetOrInfo(blocks, (e) => {
            this.handleBluetoothPrintResult(e, sheet, resolve, reject);
          });
        }).catch(err => {
          reject(new Error('生成打印指令失败: ' + err.message));
        });
      } else {
        // 小票打印机，使用原生打印方法
        try {
          Printing.printSaleSheet(sheet, false, false, null, (e) => {
            this.handleBluetoothPrintResult(e, sheet, resolve, reject);
          });
        } catch (err) {
          reject(new Error('打印失败: ' + err.message));
        }
      }
    },

    // 处理蓝牙打印结果
    handleBluetoothPrintResult(result, sheet, resolve, reject) {
      if (result.result === 'OK') {
        // 记录打印次数
        ApiPrintMark({
          operKey: this.$store.state.operKey,
          sheetType: sheet.sheetType,
          sheetIDs: sheet.sheet_id,
          printEach: true,
          printSum: false
        }).then(() => {
          resolve();
        }).catch(() => {
          resolve(); // 即使记录失败也认为打印成功
        });
      } else {
        reject(new Error(result.msg || '打印失败'));
      }
    },

    // 加载打印模板（用于批量打印）
    loadPrintTemplatesForBatch(sheetType, clientID) {
      return new Promise((resolve, reject) => {
        const params = {
          sheetType: sheetType,
          clientID: clientID
        };

        AppGetTemplate(params).then(data => {
          const templateList = data.templateList || [];
          const templates = [];

          templateList.forEach((template, i) => {
            templates.push({
              name: template.template_name,
              i: i,
              tmp: template
            });
          });

          resolve(templates);
        }).catch(err => {
          reject(err);
        });
      });
    },
    // Base64转Uint8Array
    base64ToUint8Array(base64) {
      const binaryString = window.atob(base64);
      const len = binaryString.length;
      const bytes = new Uint8Array(len);
      for (let i = 0; i < len; i++) {
        bytes[i] = binaryString.charCodeAt(i);
      }
      return bytes;
    },

    // 显示云打印结果（复用SaleSheet中的方法）
    showCloudPrintResult(res) {
      if (res.result === "OK" || res.return_msg === "打印成功" || (!res.printer_state && !res.return_msg)) {
        // 打印成功，不需要特别处理，在调用处已经处理
      } else {
        // 打印失败，错误信息已在调用处处理
      }
    },
  },
};
</script>
<style lang="less" scoped>
// height:136px
@flex_w: {
  display: flex;
  flex-wrap: wrap;
};
@flex_a_j: {
  display: flex;
  align-items: center;
  justify-content: center;
};
@flex_a_bw: {
  display: flex;
  align-items: center;
  justify-content: space-between;
};
@flex_a_end: {
  display: flex;
  align-items: center;
  justify-content: flex-end;
};
@flex_a_flex: {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
};
.navi-select-item {
  font-size: 0.65rem;
  color: #1887f7;
  border-bottom: solid 0.025rem #ccc;
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}
.public_query {
  background: #ffffff;
  height: 100%;
  .public_query_title {
    padding-top: 5px;
    border-bottom: 1px solid #f2f2f2;
    .public_query_titleSrc {
      padding: 0 10px;
      height: 35px;
      @flex_a_bw();
      margin-top: 5px;
      .public_query_titleSrc_item {
        width: 48%;
        height: 100%;
        border: 1px solid #cccccc;
        border-radius: 4px;
        overflow: hidden;
        position: relative;
        vertical-align: top;
        // div {
        //   height: 100%;
        //   width: calc(100% - 40px);
        //   padding: 0 30px 0 10px;
        //   border: none;
        //   font-size: 15px;
        //   line-height: 35px;
        //   color: #333333;
        //   text-align: left;
        // }
        input {
          height: 100%;
          width: calc(100% - 45px);
          padding: 0 45px 0 5px;
          border: none;
          font-size: 15px;
          line-height: 35px;
          color: #333333;
          vertical-align: top;
        }
        span {
          position: absolute;
          right: 0;
          top: 0;
          bottom: 0;
          width: 45px;
          font-size: 16px;
          color: #000;
          @flex_a_j();
          background: #4c99e7;
        }
        .van-icon {
          position: absolute;
          right: 0;
          top: 0;
          bottom: 0;
          width: 30px;
          font-size: 22px;
          @flex_a_j();
          color: #666666;
        }
      }
    }
    .public_list_title {
      height: 40px;
      @flex_a_bw();
      margin-top: 5px;
      padding: 0 5px;
      div {
        height: 40px;
        line-height: 40px;
        font-size: 15px;
        text-align: center;
        width: calc(25% - 10px);
        padding: 0 5px;
        font-weight: 500;
        color: #333333;
      }
      div:first-child {
        width: calc(20% - 10px);
        text-align: left;
      }
      div:last-child {
        width: calc(25% - 10px);
        text-align: right;
      }
    }
  }
}
.sales_list_boxs {
  height: calc(100% - 88px);
  overflow-x: hidden;
  overflow-y: auto;
  background: #f2f2f2;
  width: 100%;
}
.sales_list_boxs_no {
  height: calc(100% - 54px);
  @flex_a_flex();
  background: #f2f2f2;
  color: #999999;
  .whole_box_no_icon {
    font-size: 50px;
  }
  p {
    font-size: 15px;
  }
}
.sales_ul {
  width: 100%;
  height: auto;
  overflow: hidden;
  //padding: 0 5px;
  //background: #ffffff;
  li {
    width: 100%;
    height: auto;
    overflow: hidden;
    //padding: 0px 10px;
    border-bottom: 1px solid #f2f2f2;
    display: flex;
    flex-direction: column;
    align-items: center;
    // .sales_ul_t {
    //   overflow: hidden;
    //   @flex_a_bw();
    //   height: auto;
    //   div {
    //     font-size: 15px;
    //   }
    //   .sales_ul_tl {
    //     color: #333333;
    //     width: 30%;
    //     text-align: left;
    //   }
    //   .sales_ul_tc {
    //     color: #333333;
    //     text-align: left;
    //     width: 43%;
    //   }
    //   .sales_ul_tr {
    //     font-size: 15px;
    //     color: #1989fa;
    //     width: 27%;
    //     text-align: right;
    //   }
    // }
    // .sales_ul_b {
    //   overflow: hidden;
    //   @flex_a_bw();
    //   height: auto;
    //   margin-top: 5px;
    //   .sales_ul_bl {
    //     font-size: 13px;
    //     color: #666666;
    //     width: 30%;
    //     min-height: 15px;
    //     text-align: left;
    //   }
    //   .sales_ul_bc {
    //     font-size: 13px;
    //     color: #666666;
    //     width: 43%;
    //     text-align: left;
    //   }
    //   .sales_ul_br {
    //     font-size: 13px;
    //     width: 27%;
    //     color: #666666;
    //     min-height: 30px;
    //     @flex_a_end();
    //     i {
    //       font-size: 22px;
    //       color: #1989fa;
    //       margin-left: 10px;
    //     }
    //   }
    // }
    .reject-btn {
      margin-left: 10px;
      display: flex;
      color: #fff;
      height: 100%;
      width: 100px;
      font-size: 18px;
      align-items: center;
      justify-content: center;
      font-size: 12px;
      background: #ee0a24;
    }
  }
  // li:last-child {
  //   border-bottom: none;
  // }
}
.wrapper {
  position: fixed;
  left: 0px;
  bottom: 0px;
  width: 100%;
  height: 35px;
  font-size: 0.5em;
  color: #333;
  border-top: 1px solid #f2f2f2;
  box-shadow: 0 -2px 5px #f2f6fc;
  background-color: #fff;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  .content {
    padding-right: 15px;
  }
  .record {
    padding: 0 10px;
  }
  .button_print {
    margin-left: 10px;
    height: 30px;
    line-height: 30px;
    font-size: 14px;
  }
}

/* 打印加载遮罩层 */
.printing-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.6);
  z-index: 9999;
  display: flex;
  justify-content: center;
  align-items: center;
}

.printing-modal {
  background: rgba(0, 0, 0, 0.8);
  padding: 30px 40px;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  text-align: center;
}

.printing-modal .van-loading {
  position: static;
  transform: none;
  background: transparent;
  padding: 0;
  border-radius: 0;
  box-shadow: none;
}

.printing-modal .van-loading__text {
  color: #ffffff;
  font-size: 16px;
  margin-top: 12px;
}

/* 模板选择弹窗样式 */
.template-selection-wrapper {
  padding: 20px;
  max-height: 60vh;
  overflow-y: auto;
}

.template-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 1px solid #eee;
}

.template-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: bold;
}

.close-icon {
  font-size: 20px;
  color: #999;
  cursor: pointer;
}

.template-content {
  margin-bottom: 20px;
}

.no-template {
  text-align: center;
  color: #999;
  padding: 40px 0;
}

.template-list {
  max-height: 300px;
  overflow-y: auto;
}

.template-item {
  padding: 12px 0;
  border-bottom: 1px solid #f5f5f5;
}

.template-item:last-child {
  border-bottom: none;
}

.template-footer {
  display: flex;
  justify-content: space-between;
  gap: 15px;
}

.cancel-btn, .confirm-btn {
  flex: 1;
  height: 40px;
}

.cancel-btn {
  background: #f7f8fa;
  color: #646566;
  border: 1px solid #ebedf0;
}

.confirm-btn {
  background: #1989fa;
  color: white;
  border: none;
}


.sheet_wrapper{
  width: 100%;
  border-radius: 5px;
  display: flex;
  flex-direction: column;
  margin-bottom: 5px;
  padding: 6px;

  background-color: #fff;
  box-sizing:border-box;

  .sup_info{
    border-bottom: 1px solid #f2f2f2;
    margin-bottom: 2px;
    margin: 0 5px;
    padding-bottom: 4px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .sup_name{
      flex: 3;
      display: flex;
      align-items: center;
      justify-content: flex-start;
      font-size: 18px;
      font-weight: bolder;
      text-align: left;
    }
    .sup_info_right{
      display: flex;
      align-items: center;
      flex-direction: row;
      gap: 10px;
    }
    .order-source-wrapper {
      border: 1px solid #FDE3E4;
      padding: 1px 5px;
      background-color: #FDE3E4;
      border-radius: 10px;
      margin-left: 10px;
    }
    .sup_contact{
      flex: 2;
      font-size: 16px;
      display: flex;
      flex-direction: column;
      align-items: flex-end;

      .sup_tel{
        margin: 2px 0;
        a {
          color: rgb(12, 89, 190);
        }
      }
    }
  }
  .sheet_info{
       margin: 2px 5px;

    display: flex;
    .sheet_info_left{
      flex: 2;
      display: flex;
    padding: 4px 0;
      flex-direction: column;
      .sheet_no_tag{
        display: flex;
        justify-content: space-between;
        .sheet_no{
          font-size: 17px;
        }
      }
      .sheet_happentime{

        display: flex;
        color: #ccc;
      }

    }
    .sheet_tag{
      flex: 1;
      max-width: 35px;
      display: flex;
      align-items: center;
      justify-content: center;
      span {
        padding: 2px;
      }
    }
    .sheet_info_right{
      flex: 2;
      display: flex;
      font-family: numfont;
      font-size: 26px;
      justify-content: center;
      align-items: center;
      color:#C40000;
    }

  }
  .sheet_add {
    border-top: 1px solid #ddd;
    padding-top: 2px;
    width: 100%;
    color: #555;
    display: flex;
    padding-left: 5px;

  }
  .seller_senders_info{
       margin: 4px 5px 0;
    display: flex;
    justify-content: space-between;
    margin-right: 20px;
    color: #000;
  }
  .mark-brief{
    margin: 6px 5px 0;
    display: flex;
    color: #999;
  }
}

/* 批量打印按钮样式 */
.batch-print-btn {
  background-color: #ff9999 !important;
  color: #fff !important;
  border: none !important;
  border-radius: 15px !important;
  height: 30px !important;
  padding: 0 16px !important;
  font-size: 14px !important;
  white-space: nowrap !important;
  margin-left: 8px !important;
}

.batch-print-btn:active {
  background-color: #ff7777 !important;
}
</style>

